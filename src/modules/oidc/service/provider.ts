import { App, Config, ILogger, Init, Inject, Provide } from '@midwayjs/core';
import { BaseService } from '@cool-midway/core';
import Provider, {
  AdapterFactory,
  Configuration as OIDCConfiguration,
  KoaContextWithOIDC,
} from 'oidc-provider';
import { Application } from '@midwayjs/koa';
import { OIDCStoreService } from './store';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { OidcClientEntity } from '../entity/client';
import { BaseSysUserEntity } from '../../base/entity/sys/user';
import { BaseSysRoleEntity } from '../../base/entity/sys/role';
import { BaseSysDepartmentEntity } from '../../base/entity/sys/department';
import { BaseSysUserRoleEntity } from '../../base/entity/sys/user_role';

import * as mount from 'koa-mount';

@Provide()
export class OIDCProviderService extends BaseService {
  @App()
  app: Application;

  private provider: Provider;
  private issuer: string;
  private frontendIssuer: string;
  @Config('module.base')
  jwtConfig;

  @Inject()
  store: OIDCStoreService;

  @InjectEntityModel(OidcClientEntity)
  clientRepo: Repository<OidcClientEntity>;

  @InjectEntityModel(BaseSysUserEntity)
  baseSysUserEntity: Repository<BaseSysUserEntity>;

  @InjectEntityModel(BaseSysRoleEntity)
  baseSysRoleEntity: Repository<BaseSysRoleEntity>;

  @InjectEntityModel(BaseSysDepartmentEntity)
  baseSysDepartmentEntity: Repository<BaseSysDepartmentEntity>;

  @InjectEntityModel(BaseSysUserRoleEntity)
  baseSysUserRoleEntity: Repository<BaseSysUserRoleEntity>;

  @Inject()
  logger: ILogger;

  /**
   * 从前端访问地址推导出后端地址
   * 例如：http://localhost:1801/luru/oidc -> http://localhost:8002/oidc
   */
  private derivateBackendIssuer(frontendIssuer: string): string {
    try {
      const frontendUrl = new URL(frontendIssuer);

      // 默认的后端端口映射规则
      const backendPort = process.env.OIDC_BACKEND_PORT || '8002';

      // 移除路径中的代理前缀（如 /luru）
      let backendPath = frontendUrl.pathname;

      // 如果路径包含 /luru/oidc，则转换为 /oidc
      if (backendPath.includes('/luru/oidc')) {
        backendPath = backendPath.replace('/luru/oidc', '/oidc');
      } else if (backendPath.includes('/luru/')) {
        // 处理其他 /luru/ 开头的路径
        backendPath = backendPath.replace('/luru/', '/');
      }

      // 构建后端地址
      const backendIssuer = `${frontendUrl.protocol}//${frontendUrl.hostname}:${backendPort}${backendPath}`;

      this.logger.debug(`Derived backend issuer: ${frontendIssuer} -> ${backendIssuer}`);
      return backendIssuer;
    } catch (error) {
      this.logger.error('Failed to derive backend issuer, using default', error);
      return 'http://localhost:8002/oidc';
    }
  }

  private async getUserById(id: string) {
    try {
      // 根据用户名查找用户（因为我们使用username作为accountId）
      const user = await this.baseSysUserEntity
        .createQueryBuilder('u')
        .leftJoinAndSelect('u.department', 'd', 'd.id = u.departmentId')
        .where('u.username = :username AND u.status = 1', { username: id })
        .getOne();

      if (user) {
        // 获取用户角色
        const userRoles = await this.baseSysUserRoleEntity.find({
          where: { userId: user.id },
        });

        // 获取角色详细信息
        const roles = [];
        if (userRoles.length > 0) {
          const roleIds = userRoles.map(ur => ur.roleId);
          const roleEntities = await this.baseSysRoleEntity.find({
            where: roleIds.map(id => ({ id })),
          });
          roles.push(...roleEntities);
        }

        // 获取部门信息
        let departmentName = null;
        if (user.departmentId) {
          const department = await this.baseSysDepartmentEntity.findOne({
            where: { id: user.departmentId },
          });
          departmentName = department?.name;
        }

        const userData = {
          ...user,
          departmentName,
          roleIdList: userRoles.map(ur => ur.roleId),
          roles: roles.map(role => ({
            id: role.id,
            name: role.name,
            label: role.label,
          })),
        };

        this.logger.debug(`Found user data for ${id}`);
        return userData;
      }

      this.logger.warn(`No user found for id: ${id}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting user by id ${id}:`, error);
      return null;
    }
  }

  private async findClientById(clientId: string) {
    try {
      this.logger.debug(`Finding client by ID: ${clientId}`);

      // 从数据库查找客户端配置
      const client = await this.clientRepo.findOne({
        where: {
          clientId: clientId,
          status: 1,
        },
      });

      if (client) {
        // 转换数据库字段到OIDC Provider格式
        const clientConfig = {
          client_id: client.clientId,
          client_secret: client.clientSecret,
          client_name: client.name,
          redirect_uris: client.redirectUris || [],
          response_types: client.responseTypes || ['code'],
          grant_types: client.grantTypes || [
            'authorization_code',
            'refresh_token',
          ],
          scope: 'openid profile email roles',
          token_endpoint_auth_method:
            client.tokenEndpointAuthMethod || 'client_secret_basic',
          application_type: 'web',
        };

        // 移除undefined值
        Object.keys(clientConfig).forEach(key => {
          if (clientConfig[key] === undefined) {
            delete clientConfig[key];
          }
        });

        this.logger.debug(`Found client configuration for ${clientId}`);
        return clientConfig;
      }

      this.logger.warn(`Client ${clientId} not found in database`);
      return null;
    } catch (error) {
      this.logger.error(`Error finding client ${clientId}:`, error);
      return null;
    }
  }

  async initOIDCProvider() {
    // OIDC_ISSUER 代表前端访问地址（用户实际访问的地址）
    this.frontendIssuer = process.env.OIDC_ISSUER || 'http://localhost:1801/luru/oidc';

    // 自动推导后端地址：将前端地址转换为后端地址
    // 例如：http://localhost:1801/luru/oidc -> http://localhost:8002/oidc
    this.issuer = this.derivateBackendIssuer(this.frontendIssuer);

    // 记录当前使用的issuer配置
    this.logger.info(`Frontend issuer (OIDC_ISSUER): ${this.frontendIssuer}`);
    this.logger.info(`Backend issuer (derived): ${this.issuer}`);
    this.logger.info(`Environment OIDC_ISSUER: ${process.env.OIDC_ISSUER}`);

    // 配置Koa应用信任代理
    this.app.proxy = true;

    const configuration: OIDCConfiguration = {
      // 配置代理支持，让OIDC provider能够正确处理nginx代理
      proxy: true,
      features: {
        // pkce: { required: () => true },
        devInteractions: { enabled: true },
        revocation: { enabled: true },
        introspection: { enabled: true },
        registration: { enabled: true },
        clientCredentials: { enabled: true },
      },
      responseTypes: ['code'],
      grantTypes: ['authorization_code', 'refresh_token'],
      scopes: ['openid', 'profile', 'email', 'roles'],
      // 配置支持的claims
      claims: {
        openid: ['sub'],
        profile: [
          'name',
          'nickname',
          'preferred_username',
          'picture',
          'phone_number',
          'username',
          'department_id',
          'department_name',
          'status',
        ],
        email: ['email', 'email_verified'],
        roles: ['roles'],
      },
      // 配置会话过期时间
      ttl: {
        Session: (ctx, token, client) => {
          // 会话过期时间：24小时
          return 24 * 60 * 60; // 24 hours in seconds
        },
        AccessToken: (ctx, token, client) => {
          // 访问令牌过期时间：1小时
          return 60 * 60; // 1 hour in seconds
        },
        AuthorizationCode: (ctx, token, client) => {
          // 授权码过期时间：10分钟
          return 10 * 60; // 10 minutes in seconds
        },
        RefreshToken: (ctx, token, client) => {
          // 刷新令牌过期时间：30天
          return 30 * 24 * 60 * 60; // 30 days in seconds
        },
        IdToken: (ctx, token, client) => {
          // ID令牌过期时间：1小时
          return 60 * 60; // 1 hour in seconds
        },
        Interaction: () => {
          // 交互会话过期时间：60分钟
          return 60 * 60; // 60 minutes in seconds
        },
      },
      // 使用自动生成的 RSA 密钥对
      jwks: {
        keys: [
          {
            p: '1DGt2pRla-Vfkbk6J6YI3qfENC0jdBRJ-PnzhWKhT_uBt0eDB1lyg1gbHAKB1dFHdRX8IllOQZDEeJJC6V3wrBW5ecDYiCIzeXYQCB4W0NStS7iXnSsOiyD6FVp9vLn9hfk_txi8PRc2RLWABSMDjMBBPSWfAlB9Cnic0KvmLNc',
            kty: 'RSA',
            q: 'qfPXoUjcgvR9NXxc9wZpO3uV7t5lRt0JNEkcovvT5IV4o2k6jvePmlS2fkP_D3vzNtjvKc9UDqoStal_UmRejJDeDHWDNKrMyEM6j5cIJMogC_6UVbSH0pRq0jEb_qSmD4iKrtxV9D6vky4hV7xIiJ19YuKhpPmeiGCkRQLUIks',
            d: 'd3dYG1NHTmIiayZwonx2u0bd8OCJWEwEuc7OBRBMY8FcAwup8zCLVQpgye7d4K5-bManOlHgaT7ZAraY-f72ndbihG4s-1XWdbtwPTB08lGxoNe-Pueom2kgdGlm9cKUSQpgMxkFsTedf7EGQCJOKb_vuYzH7XlpmAWyQ7Qthgf-kfmVPG8MRnhX1eH2KcWFQFM-Fsgb6RThoVGTSZiKg9w_hwlZxcyCgQnpgoplfVO37wSAe_0s4cZMgQRn2sHIzL72TqDUpZTHtAKR-UUMiPrS2Hd76HC1bhIi7hUaNAD_Fz6XB_e0VtS02ljMaw-LIEaz5X8oRDLSX6Dbmd_ONQ',
            e: 'AQAB',
            qi: 'sWyurYF94fWqslbV7L6GJ6UL-I46OVqyofaacjugTf2JsGaMtGgR8-c0kvA33q0WdJz1J_xR3OrZtCEY6aH6OCbuyrNJGlxMLh663rg4ZFpcqV4VWSUe6mIs4UXKav2hh4Q-Qk1uEV7SKxMDjGmlKB-HhtWRntbauBZ2i0z9Eck',
            dp: 'IypcgoXnTPtFXFQTFDEK9UNeW7tDTeBy5eUee3N-AlBPDf229EfZl3TJgVnVBIdbTUcoctTUCD-in8y1dBFSamJrS4cvG1h3qWBshbve0hr84TilP7G8BkifyVwmzFkSb9p_uatKvuES6E4f-jn0Q6Y06ZpLgMmv_0U_z8E9Mx8',
            dq: 'PPeChRM0N64pNIS2fM9J9NKyyIvzXOZyJNWNdlJMgw_2ZPiczyiz1TNKfCU8XHuklipgMWl0yAXuzEhPqmjjGh3l6iZFuSEEcVrcqSIPA9OWh3b-E3i9uKcRYvHHDyRVhgD_bxcPppzOM0g7IdQjWQ7WKiEOx7XcTyQMCc2n990',
            n: 'jN7pqLsXebsbNsGSuJHaDdppNdoCoRk2sjTGgIKr2iopqxJxUBsv9gDm6YjgltdBXHZbc__YxVHt2aE3E80YwIWKWVVCj1No_jnoPq6y-fubB0w2U6n5nJme3Yg_tQU_Gxdc7xjHlN6A4itCgx5ZxbTKNnSDLCVg4sMYbq-Bb47Ek9-_7GdQfhp5HC6v2cW_iUgjaeUJ7szBxqUwfq5W0PFOHf7kFfW9p7eYQpHKdf5l4OjV57SbqbpCrTNsDLbKgQO8DiS84-P1W-jR3OKXce1NolqT2s30uMJaJky3IfInkO8Rf6XMwD4fHOVUI3ZI9l_uFTDzSVRms6Dgvm-w_Q',
          },
        ],
      },
      interactions: {
        url: async (ctx: KoaContextWithOIDC, interaction) => {
          // 生成前端访问的交互URL，使用配置的前端地址
          const interactionUrl = `${this.frontendIssuer}/interaction/${interaction.uid}`;

          // 记录生成的交互URL用于调试
          this.logger.info(`Generated interaction URL: ${interactionUrl} (frontend access)`);

          return interactionUrl;
        },
      },
      findAccount: async (ctx, id) => {
        try {
          // 从数据库获取用户信息
          const user = await this.getUserById(id);

          return {
            accountId: id,
            async claims(use, scope) {
              const claims: any = { sub: id };

              if (user) {
                // 根据scope返回相应的claims
                if (scope.includes('profile')) {
                  claims.name = user.name || user.nickName;
                  claims.nickname = user.nickName;
                  claims.preferred_username = user.username;
                  claims.picture = user.headImg;
                  claims.phone_number = user.phone;
                }

                if (scope.includes('email')) {
                  claims.email = user.email;
                  claims.email_verified = !!user.email;
                }

                // 添加角色信息
                if (user.roleIdList && user.roleIdList.length > 0) {
                  claims.roles = ['NORMAL'];
                }

                // 添加部门信息
                if (user.departmentId) {
                  claims.department_id = user.departmentId;
                }
                if (user.departmentName) {
                  claims.department_name = user.departmentName;
                }

                // 添加状态信息
                claims.status = user.status;
                claims.username = user.username;
              }

              return claims;
            },
          };
        } catch (error) {
          this.logger.error(`OIDC findAccount error for ${id}:`, error);
          return {
            accountId: id,
            async claims(use, scope) {
              return { sub: id };
            },
          };
        }
      },
      // 不使用静态客户端配置，而是通过adapter动态加载
      // clients: await this.loadClients(),
      // 配置 cookies 密钥
      cookies: {
        keys: [process.env.OIDC_COOKIE_SECRET || 'luru-oidccookies2025'],
        // 自定义cookie名称，避免路径冲突
        names: {
          session: '_oidc_session',
          interaction: '_oidc_interaction',
          resume: '_oidc_resume',
          state: '_oidc_state',
        },
        long: {
          signed: true,
          maxAge: 24 * 60 * 60 * 1000, // 24 hours
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          // 设置cookie路径为根路径，确保在/luru/oidc和/oidc下都可用
          path: '/',
        },
        short: {
          signed: true,
          maxAge: 10 * 60 * 1000, // 10 minutes
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          // 设置cookie路径为根路径，确保在/luru/oidc和/oidc下都可用
          path: '/',
        },
      },
    } as unknown as OIDCConfiguration;

    // 使用 Redis 适配器替代内存存储
    const storeService = this.store;
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const providerService = this;

    const Adapter: AdapterFactory = (modelName: string) => {
      return {
        name: modelName,

        key(id: string) {
          return `${modelName}:${id}`;
        },

        async upsert(id: string, payload: any, expiresIn?: number) {
          try {
            await storeService.upsert(modelName, id, payload, expiresIn);
          } catch (error) {
            providerService.logger.error('OIDC Adapter upsert error:', error);
            throw error;
          }
        },

        async find(id: string) {
          try {
            // 特殊处理Client模型，从数据库动态加载
            if (modelName === 'Client') {
              try {
                const clientData = await providerService.findClientById(id);
                if (clientData) {
                  providerService.logger.info(
                    `OIDC Adapter: Found client ${id} from database`
                  );
                  return clientData;
                } else {
                  providerService.logger.warn(
                    `OIDC Adapter: Client ${id} not found in database`
                  );
                  return undefined;
                }
              } catch (error) {
                providerService.logger.error(
                  `OIDC Adapter: Error finding client ${id}:`,
                  error
                );
                return undefined;
              }
            }

            const result = await storeService.find(modelName, id);
            return result;
          } catch (error) {
            providerService.logger.error('OIDC Adapter find error:', error);
            throw error;
          }
        },

        async findByUserCode(userCode: string) {
          try {
            return await storeService.findByUserCode(userCode);
          } catch (error) {
            providerService.logger.error(
              'OIDC Adapter findByUserCode error:',
              error
            );
            return null;
          }
        },

        async findByUid(uid: string) {
          try {
            return await storeService.findByUid(uid);
          } catch (error) {
            providerService.logger.error(
              'OIDC Adapter findByUid error:',
              error
            );
            return null;
          }
        },

        async destroy(id: string) {
          try {
            await storeService.destroy(modelName, id);
          } catch (error) {
            providerService.logger.error('OIDC Adapter destroy error:', error);
          }
        },

        async revokeByGrantId(grantId: string) {
          try {
            await storeService.revokeByGrantId(grantId);
          } catch (error) {
            providerService.logger.error(
              'OIDC Adapter revokeByGrantId error:',
              error
            );
          }
        },

        async consume(id: string) {
          try {
            await storeService.consume(modelName, id);
          } catch (error) {
            providerService.logger.error('OIDC Adapter consume error:', error);
          }
        },
      };
    };

    // 将adapter添加到configuration中
    const finalConfiguration = {
      ...configuration,
      adapter: Adapter,
    };

    this.provider = new Provider(this.issuer, finalConfiguration);

    // 添加调试中间件来查看请求信息
    this.app.use(async (ctx, next) => {
      if (ctx.path.includes('well-known') || ctx.path.includes('interaction') || ctx.path.includes('auth')) {
        this.logger.info(`OIDC request debug:`);
        this.logger.info(`  Path: ${ctx.path}`);
        this.logger.info(`  URL: ${ctx.url}`);
        this.logger.info(`  Host: ${ctx.host}`);
        this.logger.info(`  Protocol: ${ctx.protocol}`);
        this.logger.info(`  Method: ${ctx.method}`);
        if (ctx.path.includes('interaction')) {
          this.logger.info(`  Headers: ${JSON.stringify(ctx.headers, null, 2)}`);
          this.logger.info(`  Cookies: ${JSON.stringify(ctx.cookies, null, 2)}`);
        }
      }
      await next();
      if (ctx.path.includes('well-known') || ctx.path.includes('interaction') || ctx.path.includes('auth')) {
        this.logger.info(`OIDC response debug:`);
        this.logger.info(`  Status: ${ctx.status}`);
        if (ctx.path.includes('interaction') && ctx.response.header['set-cookie']) {
          this.logger.info(`  Set-Cookie: ${JSON.stringify(ctx.response.header['set-cookie'], null, 2)}`);
        }
        if (ctx.status === 404) {
          this.logger.error(`404 Error for path: ${ctx.path}`);
        }
      }
    });

    // 添加中间件修正well-known配置和重定向响应
    this.app.use(async (ctx, next) => {
      await next();

      // 调试日志
      if (ctx.path.includes('well-known') || ctx.path.includes('auth') || ctx.path.includes('interaction')) {
        this.logger.debug(`Post-processing middleware - Path: ${ctx.path}, Status: ${ctx.status}`);
        if (ctx.response.header.location) {
          this.logger.debug(`Original Location header: ${ctx.response.header.location}`);
        }
      }

      // 处理重定向响应，修正Location头中的URL
      if (ctx.response.header.location && (ctx.status === 302 || ctx.status === 301)) {
        const originalLocation = ctx.response.header.location;
        let newLocation = originalLocation;

        // 将后端地址替换为前端地址
        if (newLocation.startsWith(this.issuer)) {
          newLocation = newLocation.replace(this.issuer, this.frontendIssuer);
        } else {
          // 处理相对路径的重定向
          try {
            const backendUrl = new URL(this.issuer);
            const frontendUrl = new URL(this.frontendIssuer);

            // 如果是相对路径且以后端路径开头
            if (newLocation.startsWith(backendUrl.pathname)) {
              newLocation = newLocation.replace(backendUrl.pathname, frontendUrl.pathname);
            }
          } catch (error) {
            this.logger.warn('Failed to process relative redirect URL', error);
          }
        }

        if (newLocation !== originalLocation) {
          ctx.response.header.location = newLocation;
          this.logger.info(`Fixed redirect Location: ${originalLocation} -> ${newLocation}`);
        }
      }

      // 处理well-known配置响应，确保端点URL包含正确的路径前缀
      if (ctx.path.includes('well-known/openid-configuration') && ctx.status === 200 && ctx.body) {
        try {
          const config = typeof ctx.body === 'string' ? JSON.parse(ctx.body) : ctx.body;

          // 记录原始配置用于调试
          this.logger.info(`Original well-known config sample: ${JSON.stringify({
            authorization_endpoint: config.authorization_endpoint,
            token_endpoint: config.token_endpoint,
            issuer: config.issuer
          }, null, 2)}`);

          // 修正各个端点URL，确保包含/luru前缀
          const endpointsToFix = [
            'authorization_endpoint',
            'token_endpoint',
            'userinfo_endpoint',
            'jwks_uri',
            'registration_endpoint',
            'end_session_endpoint',
            'introspection_endpoint',
            'revocation_endpoint',
            'pushed_authorization_request_endpoint'
          ];

          endpointsToFix.forEach(endpoint => {
            if (config[endpoint]) {
              const originalUrl = config[endpoint];
              // 动态URL替换：将后端issuer地址替换为前端访问地址
              let newUrl = originalUrl;

              // 从issuer和frontendIssuer中提取基础URL
              const backendBase = this.issuer; // 例如: http://localhost:8002/oidc
              const frontendBase = this.frontendIssuer; // 例如: http://localhost:1801/luru/oidc

              // 替换后端地址为前端地址
              if (newUrl.startsWith(backendBase)) {
                // 直接替换完整的后端地址
                newUrl = newUrl.replace(backendBase, frontendBase);
              } else {
                // 处理其他可能的URL格式
                try {
                  const backendUrl = new URL(backendBase);
                  const frontendUrl = new URL(frontendBase);
                  const originalUrlObj = new URL(originalUrl);

                  // 检查是否是同一个主机和路径的变体
                  if (originalUrlObj.hostname === backendUrl.hostname &&
                      originalUrlObj.pathname.startsWith(backendUrl.pathname)) {
                    // 构建新的URL：使用前端的主机和端口，替换路径
                    const newPath = originalUrlObj.pathname.replace(backendUrl.pathname, frontendUrl.pathname);
                    newUrl = `${frontendUrl.protocol}//${frontendUrl.host}${newPath}${originalUrlObj.search}${originalUrlObj.hash}`;
                  }
                } catch (error) {
                  // URL解析失败，保持原URL不变
                  this.logger.warn(`Failed to parse URL for replacement: ${originalUrl}`, error);
                }
              }

              config[endpoint] = newUrl;

              if (originalUrl !== newUrl) {
                this.logger.info(`Fixed ${endpoint}: ${originalUrl} -> ${newUrl}`);
              }
            }
          });

          // 更新响应体
          ctx.body = JSON.stringify(config, null, 2);
          this.logger.info(`Fixed well-known configuration endpoints to include /luru prefix`);
        } catch (error) {
          this.logger.error('Error fixing well-known configuration:', error);
        }
      }
    });

    // 添加中间件查看mount内部路径处理
    this.app.use(async (ctx, next) => {
      if (ctx.path.startsWith('/oidc')) {
        this.logger.info(`Pre-mount middleware - Path: ${ctx.path}`);
      }
      await next();
    });

    // 现在直接基于issuer URL生成交互URL，不需要额外的路径处理

    // 始终挂载在 /oidc，nginx负责路径转发
    this.logger.info(`Mounting OIDC Provider at: /oidc`);

    // 使用官方推荐的 koa-mount 方式挂载 OIDC Provider
    this.app.use(mount('/oidc', this.provider));



  }



  /**
   * 重新加载客户端配置
   * 现在支持真正的动态客户端加载，无需重启应用
   */
  async reloadClients() {
    try {
      this.logger.info('Reloading OIDC client configurations...');

      // 验证新的客户端配置是否有效
      const newClients = await this.loadClients();
      this.logger.debug(`Validated ${newClients.length} client configurations`);

      // 由于我们现在使用adapter动态加载客户端，
      // 客户端配置的更改会立即生效，无需重启
      this.logger.debug(
        'Client configurations reloaded successfully. Changes are now active.'
      );

      return {
        success: true,
        message:
          'Client configurations reloaded successfully. Changes are now active.',
        clientCount: newClients.length,
      };
    } catch (error) {
      this.logger.error('Failed to reload client configurations:', error);
      return {
        success: false,
        message: 'Failed to reload client configurations: ' + error.message,
        clientCount: 0,
      };
    }
  }

  private async loadClients() {
    const clients = [] as any[];
    // 先读数据库启用的客户端
    try {
      const list = await this.clientRepo.findBy({ status: 1 });
      for (const c of list) {
        clients.push({
          client_id: c.clientId,
          client_secret: c.clientSecret,
          redirect_uris: c.redirectUris,
          grant_types: c.grantTypes?.length
            ? c.grantTypes
            : ['authorization_code', 'refresh_token'],
          response_types: c.responseTypes?.length ? c.responseTypes : ['code'],
          token_endpoint_auth_method:
            c.tokenEndpointAuthMethod || 'client_secret_basic',
        });
      }
      if (clients.length) return clients;
    } catch {}
    const env = process.env.OIDC_CLIENTS;
    if (env) {
      try {
        const parsed = JSON.parse(env);
        if (Array.isArray(parsed)) return parsed;
      } catch {}
    }
    clients.push({
      client_id: 'demo-client',
      client_secret: 'demo-secret',
      redirect_uris: ['http://localhost:3000/callback'],
      grant_types: ['authorization_code', 'refresh_token'],
      response_types: ['code'],
      token_endpoint_auth_method: 'client_secret_basic',
    });
    return clients;
  }
}
